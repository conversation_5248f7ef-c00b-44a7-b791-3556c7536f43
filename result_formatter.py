#!/opt/miniconda3/envs/wordscopy/bin/python
"""结果格式化器模块"""

import json
from typing import List, Dict, Any
from datetime import datetime
from checkers.base_checker import Issue


class ResultFormatter:
    """结果格式化器"""
    
    def __init__(self):
        self.check_time = datetime.now()
    
    def format_to_json(self, issues: List[Issue], document_path: str, 
                      rules_file: str, statistics: Dict[str, Any]) -> str:
        """
        格式化为JSON报告
        
        Args:
            issues: 问题列表
            document_path: 文档路径
            rules_file: 规则文件路径
            statistics: 文档统计信息
            
        Returns:
            str: JSON格式的报告
        """
        # 生成摘要
        summary = self._generate_summary(issues)
        
        # 转换问题为字典格式
        issues_data = [self._issue_to_dict(issue) for issue in issues]
        
        report = {
            "document": document_path,
            "check_time": self.check_time.strftime("%Y-%m-%dT%H:%M:%S"),
            "rules_file": rules_file,
            "summary": summary,
            "issues": issues_data,
            "statistics": statistics
        }
        
        return json.dumps(report, ensure_ascii=False, indent=2)
    
    def format_to_console(self, issues: List[Issue], document_path: str) -> str:
        """
        格式化为控制台输出
        
        Args:
            issues: 问题列表
            document_path: 文档路径
            
        Returns:
            str: 控制台格式的报告
        """
        lines = []
        lines.append("=" * 60)
        lines.append(f"Word文档格式检查报告")
        lines.append("=" * 60)
        lines.append(f"文档: {document_path}")
        lines.append(f"检查时间: {self.check_time.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # 摘要
        summary = self._generate_summary(issues)
        lines.append("检查摘要:")
        lines.append(f"  总问题数: {summary['total_issues']}")
        lines.append(f"  严重问题: {summary['critical']}")
        lines.append(f"  警告问题: {summary['warning']}")
        lines.append(f"  信息问题: {summary['info']}")
        lines.append("")
        
        if not issues:
            lines.append("✅ 未发现格式问题！")
            return "\n".join(lines)
        
        # 按严重程度分组显示
        critical_issues = [i for i in issues if i.severity == "critical"]
        warning_issues = [i for i in issues if i.severity == "warning"]
        info_issues = [i for i in issues if i.severity == "info"]
        
        if critical_issues:
            lines.append("🔴 严重问题:")
            lines.extend(self._format_issues_for_console(critical_issues))
            lines.append("")
        
        if warning_issues:
            lines.append("🟡 警告问题:")
            lines.extend(self._format_issues_for_console(warning_issues))
            lines.append("")
        
        if info_issues:
            lines.append("🔵 信息问题:")
            lines.extend(self._format_issues_for_console(info_issues))
            lines.append("")
        
        lines.append("=" * 60)
        
        return "\n".join(lines)
    
    def save_to_file(self, content: str, output_path: str) -> None:
        """
        保存报告到文件
        
        Args:
            content: 报告内容
            output_path: 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            raise Exception(f"保存文件失败: {e}")
    
    def _generate_summary(self, issues: List[Issue]) -> Dict[str, int]:
        """生成问题摘要"""
        summary = {
            "total_issues": len(issues),
            "critical": 0,
            "warning": 0,
            "info": 0
        }
        
        for issue in issues:
            if issue.severity in summary:
                summary[issue.severity] += 1
        
        return summary
    
    def _issue_to_dict(self, issue: Issue) -> Dict[str, Any]:
        """将Issue对象转换为字典"""
        return {
            "id": issue.id,
            "type": issue.type,
            "severity": issue.severity,
            "element_type": issue.element_type,
            "paragraph_index": issue.paragraph_index,
            "description": issue.description,
            "expected": issue.expected,
            "actual": issue.actual,
            "context": issue.context,
            "suggestion": issue.suggestion
        }
    
    def _format_issues_for_console(self, issues: List[Issue]) -> List[str]:
        """格式化问题列表为控制台显示"""
        lines = []
        
        for i, issue in enumerate(issues, 1):
            lines.append(f"  {i}. {issue.description}")
            lines.append(f"     位置: 段落 {issue.paragraph_index + 1}")
            lines.append(f"     上下文: {issue.context}")
            lines.append(f"     建议: {issue.suggestion}")
            
            # 显示期望值和实际值
            if issue.expected:
                expected_str = ", ".join([f"{k}={v}" for k, v in issue.expected.items()])
                lines.append(f"     期望: {expected_str}")
            
            if issue.actual:
                actual_str = ", ".join([f"{k}={v}" for k, v in issue.actual.items()])
                lines.append(f"     实际: {actual_str}")
            
            lines.append("")
        
        return lines
    
    def generate_html_report(self, issues: List[Issue], document_path: str, 
                           rules_file: str, statistics: Dict[str, Any]) -> str:
        """
        生成HTML格式报告（简单实现）
        
        Args:
            issues: 问题列表
            document_path: 文档路径
            rules_file: 规则文件路径
            statistics: 文档统计信息
            
        Returns:
            str: HTML格式的报告
        """
        summary = self._generate_summary(issues)
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Word文档格式检查报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-radius: 5px; }}
        .issue {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; background-color: #fafafa; }}
        .critical {{ border-left-color: #ff4444; }}
        .warning {{ border-left-color: #ffaa00; }}
        .info {{ border-left-color: #0088cc; }}
        .issue-title {{ font-weight: bold; color: #333; }}
        .issue-detail {{ margin: 5px 0; font-size: 14px; color: #666; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Word文档格式检查报告</h1>
        <p><strong>文档:</strong> {document_path}</p>
        <p><strong>检查时间:</strong> {self.check_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>规则文件:</strong> {rules_file}</p>
    </div>
    
    <div class="summary">
        <h2>检查摘要</h2>
        <p>总问题数: {summary['total_issues']}</p>
        <p>严重问题: {summary['critical']}</p>
        <p>警告问题: {summary['warning']}</p>
        <p>信息问题: {summary['info']}</p>
    </div>
    
    <div class="issues">
        <h2>问题详情</h2>
"""
        
        if not issues:
            html += "<p>✅ 未发现格式问题！</p>"
        else:
            for issue in issues:
                html += f"""
        <div class="issue {issue.severity}">
            <div class="issue-title">{issue.description}</div>
            <div class="issue-detail">位置: 段落 {issue.paragraph_index + 1}</div>
            <div class="issue-detail">上下文: {issue.context}</div>
            <div class="issue-detail">建议: {issue.suggestion}</div>
        </div>
"""
        
        html += """
    </div>
</body>
</html>
"""
        return html