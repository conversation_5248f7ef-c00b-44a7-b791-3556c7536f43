#!/opt/miniconda3/envs/wordscopy/bin/python
"""Word文档解析器模块"""

import os
from typing import List, Dict, Any, Optional, Tuple
from docx import Document
from docx.text.paragraph import Paragraph
from docx.oxml.ns import qn


class DocumentElement:
    """文档元素"""
    
    def __init__(self, paragraph: Paragraph, element_type: str, 
                 paragraph_index: int, heading_level: Optional[int] = None):
        self.paragraph = paragraph
        self.element_type = element_type
        self.paragraph_index = paragraph_index
        self.heading_level = heading_level


class DocParser:
    """Word文档解析器"""
    
    def __init__(self):
        self.document: Optional[Document] = None
        self.document_path: Optional[str] = None
    
    def load_document(self, doc_path: str) -> Document:
        """
        加载Word文档
        
        Args:
            doc_path: 文档路径
            
        Returns:
            Document: python-docx文档对象
            
        Raises:
            FileNotFoundError: 文档不存在
            Exception: 文档加载失败
        """
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"文档不存在: {doc_path}")
        
        try:
            self.document = Document(doc_path)
            self.document_path = doc_path
            return self.document
        except Exception as e:
            raise Exception(f"文档加载失败: {e}")
    
    def parse_elements(self) -> List[DocumentElement]:
        """
        解析文档元素
        
        Returns:
            List[DocumentElement]: 文档元素列表
        """
        if not self.document:
            raise ValueError("文档未加载")
        
        elements = []
        
        for i, paragraph in enumerate(self.document.paragraphs):
            # 跳过空段落（可选）
            if not paragraph.text.strip():
                continue
            
            # 识别元素类型
            element_type, heading_level = self._identify_element_type(paragraph)
            
            element = DocumentElement(
                paragraph=paragraph,
                element_type=element_type,
                paragraph_index=i,
                heading_level=heading_level
            )
            
            elements.append(element)
        
        return elements
    
    def _identify_element_type(self, paragraph: Paragraph) -> Tuple[str, Optional[int]]:
        """
        识别段落类型
        
        Args:
            paragraph: 段落对象
            
        Returns:
            Tuple[str, Optional[int]]: (元素类型, 标题级别)
        """
        # 方法1: 基于样式名称识别
        style_name = paragraph.style.name if paragraph.style else ""
        
        # 检查是否是标题样式
        heading_level = self._get_heading_level_from_style(style_name)
        if heading_level:
            return f"heading_{heading_level}", heading_level
        
        # 方法2: 基于大纲级别识别
        outline_level = self._get_outline_level(paragraph)
        if outline_level and 1 <= outline_level <= 6:
            return f"heading_{outline_level}", outline_level
        
        # 方法3: 基于文本内容模式识别（简单实现）
        text = paragraph.text.strip()
        if text:
            # 检查是否符合标题模式（如：1. 标题、第一章 标题等）
            detected_level = self._detect_heading_from_text(text)
            if detected_level:
                return f"heading_{detected_level}", detected_level
        
        # 默认为正文
        return "body_text", None
    
    def _get_heading_level_from_style(self, style_name: str) -> Optional[int]:
        """从样式名称获取标题级别"""
        style_name = style_name.lower()
        
        # 中文样式名称
        heading_styles = {
            '标题 1': 1, 'heading 1': 1, '1级标题': 1,
            '标题 2': 2, 'heading 2': 2, '2级标题': 2,
            '标题 3': 3, 'heading 3': 3, '3级标题': 3,
            '标题 4': 4, 'heading 4': 4, '4级标题': 4,
            '标题 5': 5, 'heading 5': 5, '5级标题': 5,
            '标题 6': 6, 'heading 6': 6, '6级标题': 6,
        }
        
        for style_key, level in heading_styles.items():
            if style_key in style_name:
                return level
        
        return None
    
    def _get_outline_level(self, paragraph: Paragraph) -> Optional[int]:
        """获取段落大纲级别"""
        try:
            # 尝试从段落格式获取大纲级别
            if hasattr(paragraph.paragraph_format, 'outline_level'):
                outline_level = paragraph.paragraph_format.outline_level
                if outline_level is not None and isinstance(outline_level, int):
                    return outline_level + 1  # 大纲级别从0开始，标题级别从1开始
            
            # 从XML获取大纲级别
            p_element = paragraph._element
            outline_lvl = p_element.find('.//w:outlineLvl', p_element.nsmap)
            if outline_lvl is not None:
                level = int(outline_lvl.get(qn('w:val')))
                return level + 1 if level < 9 else None
            
        except Exception:
            pass
        
        return None
    
    def _detect_heading_from_text(self, text: str) -> Optional[int]:
        """从文本内容检测标题级别（简单实现）"""
        import re
        
        # 匹配常见标题模式
        patterns = [
            (r'^第[一二三四五六七八九十\d+]章\s', 1),  # 第一章
            (r'^第[一二三四五六七八九十\d+]节\s', 2),  # 第一节
            (r'^[一二三四五六七八九十]\s*[、.]\s*', 2),  # 一、标题
            (r'^\d+\s*[、.]\s*', 2),  # 1. 标题
            (r'^\d+\.\d+\s+', 3),  # 1.1 标题
            (r'^\d+\.\d+\.\d+\s+', 4),  # 1.1.1 标题
            (r'^\(\d+\)\s*', 4),  # (1) 标题
        ]
        
        for pattern, level in patterns:
            if re.match(pattern, text):
                return level
        
        return None
    
    def get_document_statistics(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        if not self.document:
            raise ValueError("文档未加载")
        
        elements = self.parse_elements()
        
        stats = {
            "total_paragraphs": len(self.document.paragraphs),
            "checked_paragraphs": len(elements),
            "total_headings": {},
            "total_figures": 0,
            "total_tables": len(self.document.tables)
        }
        
        # 统计各级标题数量
        for level in range(1, 7):
            heading_count = len([e for e in elements 
                               if e.element_type == f"heading_{level}"])
            if heading_count > 0:
                stats["total_headings"][f"level_{level}"] = heading_count
        
        return stats