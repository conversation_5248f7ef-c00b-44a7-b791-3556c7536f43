#!/opt/miniconda3/envs/wordscopy/bin/python
"""字体检查器模块"""

import re
from typing import List, Dict, Any
from docx.text.run import Run
from .base_checker import BaseChecker, Issue


class FontChecker(BaseChecker):
    """字体检查器"""
    
    def __init__(self, rules: Dict[str, Any]):
        super().__init__(rules)
        # 中文字符Unicode范围
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\u20000-\u2a6df\u2a700-\u2b73f\u2b740-\u2b81f\u2b820-\u2ceaf\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u2f800-\u2fa1f]')
        # 英文字符（包括数字和标点）
        self.english_pattern = re.compile(r'[a-zA-Z0-9\s\.,;:!?\'"()[\]{}\-_+=@#$%^&*<>/\\|`~]')
    
    def check(self, element: Any, element_type: str, paragraph_index: int) -> List[Issue]:
        """检查字体格式"""
        issues = []
        
        # 获取对应的规则
        rule = self._get_rule_for_element(element_type)
        if not rule or 'font' not in rule:
            return issues
        
        font_rules = rule['font']
        size_rule = rule.get('size')
        
        # 检查段落中的每个run
        if hasattr(element, 'runs'):
            for run_idx, run in enumerate(element.runs):
                run_issues = self._check_run_font(
                    run, font_rules, size_rule, element_type, 
                    paragraph_index, run_idx
                )
                issues.extend(run_issues)
        
        return issues
    
    def _check_run_font(self, run: Run, font_rules: Dict[str, str], 
                       size_rule: int, element_type: str, 
                       paragraph_index: int, run_idx: int) -> List[Issue]:
        """检查单个run的字体"""
        issues = []
        
        if not run.text.strip():
            return issues
        
        # 分析文本中的字符类型
        chinese_chars = self.chinese_pattern.findall(run.text)
        english_chars = self.english_pattern.findall(run.text)
        
        # 获取实际字体和字号
        actual_font = run.font.name if run.font.name else "未指定"
        actual_size = run.font.size.pt if run.font.size else None
        
        # 检查字体
        if chinese_chars and font_rules.get('chinese'):
            expected_font = font_rules['chinese']
            if actual_font != expected_font:
                issues.append(self._create_font_issue(
                    "chinese_font_error", element_type, paragraph_index,
                    run.text, expected_font, actual_font, size_rule, actual_size
                ))
        
        if english_chars and font_rules.get('english'):
            expected_font = font_rules['english']
            if actual_font != expected_font:
                issues.append(self._create_font_issue(
                    "english_font_error", element_type, paragraph_index,
                    run.text, expected_font, actual_font, size_rule, actual_size
                ))
        
        # 检查字号
        if size_rule and actual_size and actual_size != size_rule:
            issues.append(self._create_size_issue(
                element_type, paragraph_index, run.text,
                size_rule, actual_size, actual_font
            ))
        
        return issues
    
    def _create_font_issue(self, issue_type: str, element_type: str, 
                          paragraph_index: int, context: str,
                          expected_font: str, actual_font: str,
                          expected_size: int, actual_size: float) -> Issue:
        """创建字体问题"""
        char_type = "中文" if issue_type == "chinese_font_error" else "英文"
        
        return Issue(
            id=self._generate_issue_id(issue_type, paragraph_index),
            type="font_error",
            severity="critical",
            element_type=element_type,
            paragraph_index=paragraph_index,
            description=f"{element_type}中{char_type}字体不符合规范",
            expected={
                "font": expected_font,
                "size": expected_size
            },
            actual={
                "font": actual_font,
                "size": actual_size
            },
            context=self._get_context_with_text(context),
            suggestion=f"将{char_type}字体修改为'{expected_font}'"
        )
    
    def _create_size_issue(self, element_type: str, paragraph_index: int, 
                          context: str, expected_size: int, actual_size: float,
                          font_name: str) -> Issue:
        """创建字号问题"""
        return Issue(
            id=self._generate_issue_id("font_size_error", paragraph_index),
            type="font_size_error",
            severity="warning",
            element_type=element_type,
            paragraph_index=paragraph_index,
            description=f"{element_type}字号不符合规范",
            expected={
                "size": expected_size,
                "font": font_name
            },
            actual={
                "size": actual_size,
                "font": font_name
            },
            context=self._get_context_with_text(context),
            suggestion=f"将字号调整为{expected_size}磅"
        )
    
    def _get_context_with_text(self, text: str, max_length: int = 30) -> str:
        """获取带文本的上下文"""
        if not text or not text.strip():
            return "空文本"
        
        text = text.strip()
        if len(text) <= max_length:
            return text
        return text[:max_length] + "..."
    
    def _is_chinese_text(self, text: str) -> bool:
        """判断是否主要包含中文字符"""
        if not text:
            return False
        chinese_count = len(self.chinese_pattern.findall(text))
        total_count = len(text.strip())
        return chinese_count / total_count > 0.5 if total_count > 0 else False
    
    def _is_english_text(self, text: str) -> bool:
        """判断是否主要包含英文字符"""
        if not text:
            return False
        english_count = len(self.english_pattern.findall(text))
        total_count = len(text.strip())
        return english_count / total_count > 0.5 if total_count > 0 else False