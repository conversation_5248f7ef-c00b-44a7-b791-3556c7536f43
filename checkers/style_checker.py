#!/opt/miniconda3/envs/wordscopy/bin/python
"""样式检查器模块"""

from typing import List, Dict, Any
from docx.text.paragraph import Paragraph
from docx.enum.text import WD_ALIGN_PARAGRAPH
from .base_checker import <PERSON><PERSON><PERSON><PERSON>, Issue


class StyleChecker(BaseChecker):
    """样式检查器"""
    
    def __init__(self, rules: Dict[str, Any]):
        super().__init__(rules)
        # 对齐方式映射
        self.alignment_map = {
            WD_ALIGN_PARAGRAPH.LEFT: "left",
            WD_ALIGN_PARAGRAPH.CENTER: "center",
            WD_ALIGN_PARAGRAPH.RIGHT: "right",
            WD_ALIGN_PARAGRAPH.JUSTIFY: "justify",
            WD_ALIGN_PARAGRAPH.DISTRIBUTE: "distribute"
        }
        
        # 反向映射
        self.alignment_reverse_map = {v: k for k, v in self.alignment_map.items()}
    
    def check(self, element: Any, element_type: str, paragraph_index: int) -> List[Issue]:
        """检查样式格式"""
        issues = []
        
        if not isinstance(element, Paragraph):
            return issues
        
        # 获取对应的规则
        rule = self._get_rule_for_element(element_type)
        if not rule:
            return issues
        
        # 检查对齐方式
        if 'alignment' in rule:
            alignment_issues = self._check_alignment(
                element, rule['alignment'], element_type, paragraph_index
            )
            issues.extend(alignment_issues)
        
        # 检查加粗
        if 'bold' in rule:
            bold_issues = self._check_bold(
                element, rule['bold'], element_type, paragraph_index
            )
            issues.extend(bold_issues)
        
        # 检查首行缩进（仅对正文）
        if element_type == "body_text" and 'first_line_indent' in rule:
            indent_issues = self._check_first_line_indent(
                element, rule['first_line_indent'], element_type, paragraph_index
            )
            issues.extend(indent_issues)
        
        return issues
    
    def _check_alignment(self, paragraph: Paragraph, expected_alignment: str,
                        element_type: str, paragraph_index: int) -> List[Issue]:
        """检查对齐方式"""
        issues = []
        
        actual_alignment = self._get_paragraph_alignment(paragraph)
        
        if actual_alignment != expected_alignment:
            alignment_names = {
                "left": "左对齐",
                "center": "居中对齐", 
                "right": "右对齐",
                "justify": "两端对齐",
                "distribute": "分散对齐"
            }
            
            issues.append(Issue(
                id=self._generate_issue_id("alignment_error", paragraph_index),
                type="style_error",
                severity="warning",
                element_type=element_type,
                paragraph_index=paragraph_index,
                description=f"{element_type}对齐方式不符合规范",
                expected={"alignment": expected_alignment},
                actual={"alignment": actual_alignment},
                context=self._get_context(paragraph),
                suggestion=f"将对齐方式修改为{alignment_names.get(expected_alignment, expected_alignment)}"
            ))
        
        return issues
    
    def _check_bold(self, paragraph: Paragraph, expected_bold: bool,
                   element_type: str, paragraph_index: int) -> List[Issue]:
        """检查加粗格式"""
        issues = []
        
        # 检查段落中主要文本是否加粗
        actual_bold = self._is_paragraph_bold(paragraph)
        
        if actual_bold != expected_bold:
            issues.append(Issue(
                id=self._generate_issue_id("bold_error", paragraph_index),
                type="style_error",
                severity="info",
                element_type=element_type,
                paragraph_index=paragraph_index,
                description=f"{element_type}加粗格式不符合规范",
                expected={"bold": expected_bold},
                actual={"bold": actual_bold},
                context=self._get_context(paragraph),
                suggestion=f"{'设置加粗' if expected_bold else '取消加粗'}"
            ))
        
        return issues
    
    def _check_first_line_indent(self, paragraph: Paragraph, expected_indent: int,
                                element_type: str, paragraph_index: int) -> List[Issue]:
        """检查首行缩进"""
        issues = []
        
        actual_indent = self._get_first_line_indent(paragraph)
        
        if actual_indent is None:
            return issues
        
        # 转换为字符数（假设一个字符约为12磅）
        expected_indent_pt = expected_indent * 12
        tolerance = 6  # 允许0.5字符的误差
        
        if abs(actual_indent - expected_indent_pt) > tolerance:
            issues.append(Issue(
                id=self._generate_issue_id("indent_error", paragraph_index),
                type="style_error",
                severity="info",
                element_type=element_type,
                paragraph_index=paragraph_index,
                description=f"{element_type}首行缩进不符合规范",
                expected={"first_line_indent": expected_indent},
                actual={"first_line_indent": round(actual_indent / 12, 1)},
                context=self._get_context(paragraph),
                suggestion=f"将首行缩进调整为{expected_indent}字符"
            ))
        
        return issues
    
    def _get_paragraph_alignment(self, paragraph: Paragraph) -> str:
        """获取段落对齐方式"""
        try:
            alignment = paragraph.alignment
            if alignment is None:
                return "left"  # 默认左对齐
            
            return self.alignment_map.get(alignment, "left")
        except Exception:
            return "left"
    
    def _is_paragraph_bold(self, paragraph: Paragraph) -> bool:
        """判断段落是否主要为加粗"""
        try:
            bold_count = 0
            total_count = 0
            
            for run in paragraph.runs:
                if run.text.strip():  # 只统计非空文本
                    total_count += len(run.text.strip())
                    if run.bold:
                        bold_count += len(run.text.strip())
            
            if total_count == 0:
                return False
            
            # 如果超过50%的文本是加粗的，认为段落是加粗的
            return bold_count / total_count > 0.5
        except Exception:
            return False
    
    def _get_first_line_indent(self, paragraph: Paragraph) -> float:
        """获取首行缩进（磅）"""
        try:
            if paragraph.paragraph_format.first_line_indent is None:
                return 0
            
            if hasattr(paragraph.paragraph_format.first_line_indent, 'pt'):
                return paragraph.paragraph_format.first_line_indent.pt
            
            return float(paragraph.paragraph_format.first_line_indent)
        except Exception:
            return None