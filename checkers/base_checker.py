#!/opt/miniconda3/envs/wordscopy/bin/python
"""基础检查器模块"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class Issue:
    """检查发现的问题"""
    id: str
    type: str
    severity: str  # critical, warning, info
    element_type: str  # body_text, heading_1, heading_2, etc.
    paragraph_index: int
    description: str
    expected: Dict[str, Any]
    actual: Dict[str, Any]
    context: str
    suggestion: str


class BaseChecker(ABC):
    """基础检查器抽象类"""
    
    def __init__(self, rules: Dict[str, Any]):
        """
        初始化检查器
        
        Args:
            rules: 检查规则配置
        """
        self.rules = rules
    
    @abstractmethod
    def check(self, element: Any, element_type: str, paragraph_index: int) -> List[Issue]:
        """
        执行检查
        
        Args:
            element: 要检查的元素（段落、运行等）
            element_type: 元素类型
            paragraph_index: 段落索引
            
        Returns:
            List[Issue]: 发现的问题列表
        """
        pass
    
    def _generate_issue_id(self, issue_type: str, paragraph_index: int) -> str:
        """生成问题ID"""
        return f"{issue_type}_{paragraph_index}_{hash(paragraph_index) % 1000:03d}"
    
    def _get_context(self, element: Any, max_length: int = 50) -> str:
        """获取元素上下文"""
        try:
            if hasattr(element, 'text'):
                text = element.text
            else:
                text = str(element)
            
            if len(text) <= max_length:
                return text
            return text[:max_length] + "..."
        except Exception:
            return "无法获取上下文"
    
    def _get_rule_for_element(self, element_type: str) -> Optional[Dict[str, Any]]:
        """获取元素类型对应的规则"""
        if element_type == "body_text":
            return self.rules.get("body", {})
        elif element_type.startswith("heading_"):
            level = element_type.split("_")[1]
            return self.rules.get("headings", {}).get(f"level_{level}", {})
        return None