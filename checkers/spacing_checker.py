#!/opt/miniconda3/envs/wordscopy/bin/python
"""间距检查器模块"""

from typing import List, Dict, Any
from docx.shared import Pt
from docx.text.paragraph import Paragraph
from .base_checker import <PERSON><PERSON>he<PERSON>, Issue


class SpacingChecker(BaseChecker):
    """间距检查器"""
    
    def check(self, element: Any, element_type: str, paragraph_index: int) -> List[Issue]:
        """检查间距格式"""
        issues = []
        
        if not isinstance(element, Paragraph):
            return issues
        
        # 获取对应的规则
        rule = self._get_rule_for_element(element_type)
        if not rule:
            return issues
        
        # 检查行间距
        if 'line_spacing' in rule:
            line_spacing_issues = self._check_line_spacing(
                element, rule['line_spacing'], element_type, paragraph_index
            )
            issues.extend(line_spacing_issues)
        
        # 检查段前间距
        if 'spacing_before' in rule:
            before_spacing_issues = self._check_spacing_before(
                element, rule['spacing_before'], element_type, paragraph_index
            )
            issues.extend(before_spacing_issues)
        
        # 检查段后间距
        if 'spacing_after' in rule:
            after_spacing_issues = self._check_spacing_after(
                element, rule['spacing_after'], element_type, paragraph_index
            )
            issues.extend(after_spacing_issues)
        
        return issues
    
    def _check_line_spacing(self, paragraph: Paragraph, expected_spacing: float,
                           element_type: str, paragraph_index: int) -> List[Issue]:
        """检查行间距"""
        issues = []
        
        # 获取实际行间距
        actual_spacing = self._get_line_spacing(paragraph)
        
        if actual_spacing is None:
            return issues
        
        # 允许小的误差（0.1倍行距）
        tolerance = 0.1
        if abs(actual_spacing - expected_spacing) > tolerance:
            issues.append(Issue(
                id=self._generate_issue_id("line_spacing_error", paragraph_index),
                type="spacing_error",
                severity="warning",
                element_type=element_type,
                paragraph_index=paragraph_index,
                description=f"{element_type}行间距不符合规范",
                expected={"line_spacing": expected_spacing},
                actual={"line_spacing": actual_spacing},
                context=self._get_context(paragraph),
                suggestion=f"将行间距调整为{expected_spacing}倍"
            ))
        
        return issues
    
    def _check_spacing_before(self, paragraph: Paragraph, expected_spacing: int,
                             element_type: str, paragraph_index: int) -> List[Issue]:
        """检查段前间距"""
        issues = []
        
        actual_spacing = self._get_spacing_before(paragraph)
        
        if actual_spacing is None:
            return issues
        
        # 允许1磅的误差
        tolerance = 1
        if abs(actual_spacing - expected_spacing) > tolerance:
            issues.append(Issue(
                id=self._generate_issue_id("spacing_before_error", paragraph_index),
                type="spacing_error",
                severity="info",
                element_type=element_type,
                paragraph_index=paragraph_index,
                description=f"{element_type}段前间距不符合规范",
                expected={"spacing_before": expected_spacing},
                actual={"spacing_before": actual_spacing},
                context=self._get_context(paragraph),
                suggestion=f"将段前间距调整为{expected_spacing}磅"
            ))
        
        return issues
    
    def _check_spacing_after(self, paragraph: Paragraph, expected_spacing: int,
                            element_type: str, paragraph_index: int) -> List[Issue]:
        """检查段后间距"""
        issues = []
        
        actual_spacing = self._get_spacing_after(paragraph)
        
        if actual_spacing is None:
            return issues
        
        # 允许1磅的误差
        tolerance = 1
        if abs(actual_spacing - expected_spacing) > tolerance:
            issues.append(Issue(
                id=self._generate_issue_id("spacing_after_error", paragraph_index),
                type="spacing_error",
                severity="info",
                element_type=element_type,
                paragraph_index=paragraph_index,
                description=f"{element_type}段后间距不符合规范",
                expected={"spacing_after": expected_spacing},
                actual={"spacing_after": actual_spacing},
                context=self._get_context(paragraph),
                suggestion=f"将段后间距调整为{expected_spacing}磅"
            ))
        
        return issues
    
    def _get_line_spacing(self, paragraph: Paragraph) -> float:
        """获取段落行间距"""
        try:
            if paragraph.paragraph_format.line_spacing is None:
                return None
            
            # 如果是倍数行距
            if paragraph.paragraph_format.line_spacing_rule is not None:
                # WdLineSpacing.MULTIPLE = 5
                if hasattr(paragraph.paragraph_format.line_spacing_rule, 'value'):
                    if paragraph.paragraph_format.line_spacing_rule.value == 5:
                        return paragraph.paragraph_format.line_spacing
                elif paragraph.paragraph_format.line_spacing_rule == 5:
                    return paragraph.paragraph_format.line_spacing
            
            # 如果是固定值，转换为倍数（以12磅为基准）
            if hasattr(paragraph.paragraph_format.line_spacing, 'pt'):
                return paragraph.paragraph_format.line_spacing.pt / 12.0
            
            return paragraph.paragraph_format.line_spacing
        except Exception:
            return None
    
    def _get_spacing_before(self, paragraph: Paragraph) -> float:
        """获取段前间距（磅）"""
        try:
            if paragraph.paragraph_format.space_before is None:
                return 0
            
            if hasattr(paragraph.paragraph_format.space_before, 'pt'):
                return paragraph.paragraph_format.space_before.pt
            
            return float(paragraph.paragraph_format.space_before)
        except Exception:
            return None
    
    def _get_spacing_after(self, paragraph: Paragraph) -> float:
        """获取段后间距（磅）"""
        try:
            if paragraph.paragraph_format.space_after is None:
                return 0
            
            if hasattr(paragraph.paragraph_format.space_after, 'pt'):
                return paragraph.paragraph_format.space_after.pt
            
            return float(paragraph.paragraph_format.space_after)
        except Exception:
            return None