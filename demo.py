#!/opt/miniconda3/envs/wordscopy/bin/python
"""Word文档格式检查工具演示脚本"""

import os
import subprocess
import sys

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"演示: {description}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='/root/workspace/doc_check')
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"执行失败: {e}")
        return False

def main():
    """主演示流程"""
    print("Word文档格式检查工具 - 功能演示")
    print("=" * 60)
    
    # 确保测试文档存在
    test_doc = "test/test_document.docx"
    if not os.path.exists(test_doc):
        print("创建测试文档...")
        subprocess.run([
            '/opt/miniconda3/envs/wordscopy/bin/python', 
            'test/create_test_doc.py'
        ], cwd='/root/workspace/doc_check')
    
    # 演示基本功能
    demos = [
        {
            "cmd": ['/opt/miniconda3/envs/wordscopy/bin/python', 'main.py', test_doc],
            "desc": "基本格式检查（控制台输出）"
        },
        {
            "cmd": ['/opt/miniconda3/envs/wordscopy/bin/python', 'main.py', test_doc, 
                   '--format', 'json', '--output', 'demo_report.json'],
            "desc": "生成JSON格式报告"
        },
        {
            "cmd": ['/opt/miniconda3/envs/wordscopy/bin/python', 'main.py', test_doc, 
                   '--severity', 'critical'],
            "desc": "仅显示严重问题"
        },
        {
            "cmd": ['/opt/miniconda3/envs/wordscopy/bin/python', 'main.py', test_doc, 
                   '--format', 'html', '--output', 'demo_report.html'],
            "desc": "生成HTML格式报告"
        }
    ]
    
    success_count = 0
    for demo in demos:
        if run_command(demo["cmd"], demo["desc"]):
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"演示完成! 成功执行 {success_count}/{len(demos)} 个演示")
    print(f"{'='*60}")
    
    # 显示生成的文件
    output_files = ['demo_report.json', 'demo_report.html']
    for file in output_files:
        if os.path.exists(file):
            print(f"✅ 已生成: {file}")
        else:
            print(f"❌ 未生成: {file}")

if __name__ == "__main__":
    main()