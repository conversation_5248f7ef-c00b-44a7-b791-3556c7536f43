# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

## 开发环境

### Python 解释器
- 唯一指定路径: `/opt/miniconda3/envs/wordscopy/bin/python`
- 所有脚本必须使用此绝对路径运行

### 代码格式化和检查
```bash
# 使用 black 格式化
black *.py app/

# 使用 flake8 检查代码风格
flake8 *.py app/

# 类型检查
mypy app/
```

## 开发原则

- 保持项目文件和结构清晰规范
- 函数设计遵循单一职责，注释保持简洁清晰
- 变量名、函数名使用蛇形命名法，类名使用帕斯卡命名法，命名应清晰、无歧义
- 遵循 PEP 8 规范，使用 black 进行代码自动格式化，使用 flake8 进行代码风格检查
- 始终保持项目根目录 & main.py 最简洁

## 注意事项

- 始终在项目根目录运行命令
- 始终使用项目 python 解释器的**绝对路径**运行和测试
- 始终使用中文进行思考和回答
- 日志文件放在 `logs/` 目录下
- 测试代码始终在 `test/` 目录下创建，不要放在项目根目录