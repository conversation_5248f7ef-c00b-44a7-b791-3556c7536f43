# Word文档格式检查工具 MVP 实现方案

## 一、技术架构设计

### 1. 技术选型
- **核心库**: 
  - `python-docx`: 解析和操作Word文档
  - `pyyaml`: 配置文件解析
  - `jsonschema`: 配置文件验证
- **架构模式**: 模块化设计，策略模式实现规则检查

### 2. 系统架构

```
┌─────────────────────────────────────────────┐
│           主程序 (main.py)                   │
├─────────────────────────────────────────────┤
│     配置管理器 (config_manager.py)           │
├─────────────────────────────────────────────┤
│     文档解析器 (doc_parser.py)               │
├─────────────────────────────────────────────┤
│     规则检查引擎 (rule_engine.py)            │
│  ┌──────────────────────────────────────┐   │
│  │  - 字体检查器 (font_checker)         │   │
│  │  - 字号检查器 (size_checker)         │   │
│  │  - 行间距检查器 (spacing_checker)    │   │
│  │  - 样式检查器 (style_checker)        │   │
│  └──────────────────────────────────────┘   │
├─────────────────────────────────────────────┤
│     结果生成器 (result_formatter.py)         │
└─────────────────────────────────────────────┘
```

## 二、配置文件设计

### 配置文件格式 (rules.yaml)

```yaml
# Word文档格式检查规则配置
version: "1.0"
name: "标准文档格式规则"

# 正文格式规则
body:
  font:
    chinese: "宋体"           # 中文字体
    english: "Times New Roman" # 英文字体
  size: 12                    # 字号（磅）
  line_spacing: 1.5           # 行间距（倍数）
  alignment: "justify"        # 对齐方式
  first_line_indent: 2        # 首行缩进（字符）

# 标题格式规则
headings:
  level_1:
    font:
      chinese: "黑体"
      english: "Arial"
    size: 16
    bold: true
    line_spacing: 1.5
    spacing_before: 12        # 段前间距（磅）
    spacing_after: 6          # 段后间距（磅）
    alignment: "center"
    
  level_2:
    font:
      chinese: "黑体"
      english: "Arial"
    size: 14
    bold: true
    line_spacing: 1.5
    spacing_before: 12
    spacing_after: 6
    alignment: "left"
    
  # ... level_3 到 level_6 类似配置

# 图表标题格式规则
captions:
  figure:
    font:
      chinese: "宋体"
      english: "Times New Roman"
    size: 10
    bold: false
    alignment: "center"
    position: "below"         # 位置：图下方
    
  table:
    font:
      chinese: "黑体"
      english: "Arial"
    size: 10
    bold: true
    alignment: "center"
    position: "above"         # 位置：表上方

# 检查选项
options:
  check_empty_paragraphs: true    # 检查空段落
  check_spacing_consistency: true # 检查间距一致性
  strict_mode: false              # 严格模式
```

## 三、规则检查引擎设计

### 核心检查类设计

```python
# 基础检查器接口
class BaseChecker:
    def check(self, element, rule) -> List[Issue]:
        pass

# 字体检查器
class FontChecker(BaseChecker):
    def check(self, run, rule):
        # 检查中英文字体
        # 识别中英文字符
        # 返回不符合规则的问题
        pass

# 段落检查器
class ParagraphChecker(BaseChecker):
    def check(self, paragraph, rule):
        # 检查行间距
        # 检查缩进
        # 检查对齐方式
        pass

# 标题检查器
class HeadingChecker(BaseChecker):
    def check(self, paragraph, heading_level, rule):
        # 识别标题级别
        # 检查标题格式
        # 检查编号格式
        pass
```

### 检查流程

1. **文档解析**：逐段落、逐运行(run)解析
2. **元素识别**：识别正文、标题、图表标题
3. **规则匹配**：根据元素类型匹配对应规则
4. **格式检查**：执行具体格式检查
5. **问题收集**：记录所有格式问题

## 四、输出格式设计

### JSON输出格式

```json
{
  "document": "example.docx",
  "check_time": "2024-01-01T10:30:00",
  "rules_file": "rules.yaml",
  "summary": {
    "total_issues": 15,
    "critical": 3,
    "warning": 8,
    "info": 4
  },
  "issues": [
    {
      "id": "001",
      "type": "font_error",
      "severity": "critical",
      "element_type": "body_text",
      "page": 3,
      "paragraph_index": 12,
      "description": "正文中文字体不符合规范",
      "expected": {
        "font": "宋体",
        "size": 12
      },
      "actual": {
        "font": "微软雅黑",
        "size": 12
      },
      "context": "...这是一段示例文本，展示上下文内容...",
      "suggestion": "将字体修改为'宋体'"
    },
    {
      "id": "002",
      "type": "spacing_error",
      "severity": "warning",
      "element_type": "heading_1",
      "page": 5,
      "paragraph_index": 20,
      "description": "一级标题行间距不符合规范",
      "expected": {
        "line_spacing": 1.5
      },
      "actual": {
        "line_spacing": 1.0
      },
      "context": "第一章 引言",
      "suggestion": "将行间距调整为1.5倍"
    }
  ],
  "statistics": {
    "total_paragraphs": 156,
    "checked_paragraphs": 156,
    "total_headings": {
      "level_1": 5,
      "level_2": 12,
      "level_3": 8
    },
    "total_figures": 10,
    "total_tables": 5
  }
}
```

## 五、MVP功能边界

### MVP版本功能范围

**包含功能：**
1. ✅ 基础格式检查
   - 正文字体、字号（区分中英文）
   - 1-6级标题格式检查
   - 行间距、段间距检查

2. ✅ 配置文件支持
   - YAML格式配置读取
   - 规则验证
   - 默认规则模板

3. ✅ 结果输出
   - JSON格式报告
   - 错误定位（段落索引）
   - 上下文展示
   - 修改建议

**暂不包含功能：**
1. ❌ 页码精确定位（需要复杂的页面计算）
2. ❌ 复杂的表格/图片格式检查
3. ❌ 自动修复功能
4. ❌ GUI界面

## 六、实现计划

### 第一阶段：基础架构
- 项目结构搭建
- 配置文件解析模块
- 基础文档解析功能

### 第二阶段：核心功能
- 实现字体检查器
- 实现字号检查器
- 实现行间距检查器
- 实现标题识别和检查

### 第三阶段：完善功能
- JSON报告生成
- 错误定位优化
- 上下文提取
- 单元测试

### 第四阶段：测试优化
- 集成测试
- 性能优化
- 文档编写

## 七、项目结构

```
doc_checker/
├── src/
│   ├── __init__.py
│   ├── main.py                 # 主程序入口
│   ├── config_manager.py       # 配置管理
│   ├── doc_parser.py          # Word文档解析
│   ├── rule_engine.py         # 规则引擎
│   ├── checkers/              # 检查器模块
│   │   ├── __init__.py
│   │   ├── base_checker.py
│   │   ├── font_checker.py
│   │   ├── spacing_checker.py
│   │   └── style_checker.py
│   └── result_formatter.py    # 结果格式化
├── config/
│   └── default_rules.yaml     # 默认规则配置
├── tests/                      # 测试文件
├── examples/                   # 示例文档
├── requirements.txt           # 依赖包
└── README.md                  # 项目说明
```

## 八、关键技术点

1. **中英文识别**：使用Unicode范围判断字符类型
2. **标题识别**：基于样式名称和大纲级别
3. **页码计算**：MVP版本使用段落索引替代
4. **性能优化**：大文档分块处理

## 总结

这个MVP实现方案采用了模块化设计，使用Python和python-docx库作为核心技术栈。方案具有以下特点：

**优势：**
- 配置灵活，易于扩展
- 输出格式清晰，便于集成
- 架构清晰，易于维护
- 支持中英文混合文档

**技术风险及应对：**
- 页码定位问题 → MVP使用段落索引
- 复杂格式识别 → 逐步迭代完善
- 性能问题 → 分块处理优化

这个MVP版本可以在1-2周内完成开发，满足基础的文档格式检查需求。后续可根据使用反馈逐步增加高级功能。