#!/opt/miniconda3/envs/wordscopy/bin/python
"""Word文档格式检查工具主程序"""

import argparse
import sys
import os
from pathlib import Path
from typing import Optional

from config_manager import ConfigManager
from doc_parser import DocParser
from rule_engine import RuleEngine
from result_formatter import ResultFormatter


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(
        description="Word文档格式检查工具",
        epilog="示例: python main.py document.docx"
    )
    
    parser.add_argument(
        "document",
        help="要检查的Word文档路径"
    )
    
    parser.add_argument(
        "-c", "--config",
        help="配置文件路径，默认使用内置配置",
        default=None
    )
    
    parser.add_argument(
        "-o", "--output",
        help="输出报告文件路径（JSON格式）",
        default=None
    )
    
    parser.add_argument(
        "--format",
        choices=["console", "json", "html"],
        default="console",
        help="输出格式，默认为控制台输出"
    )
    
    parser.add_argument(
        "--severity",
        choices=["critical", "warning", "info"],
        action="append",
        help="只显示指定严重级别的问题，可多次使用"
    )
    
    parser.add_argument(
        "--quiet",
        action="store_true",
        help="静默模式，只输出到文件"
    )
    
    args = parser.parse_args()
    
    try:
        # 检查文档文件是否存在
        if not os.path.exists(args.document):
            print(f"错误: 文档文件不存在: {args.document}", file=sys.stderr)
            return 1
        
        # 加载配置
        config_manager = ConfigManager()
        config_path = args.config or config_manager.get_default_config_path()
        
        if not os.path.exists(config_path):
            print(f"错误: 配置文件不存在: {config_path}", file=sys.stderr)
            return 1
        
        print(f"加载配置文件: {config_path}")
        rules = config_manager.load_config(config_path)
        
        # 解析文档
        print(f"解析文档: {args.document}")
        doc_parser = DocParser()
        document = doc_parser.load_document(args.document)
        elements = doc_parser.parse_elements()
        statistics = doc_parser.get_document_statistics()
        
        print(f"解析完成，共找到 {len(elements)} 个有效元素")
        
        # 执行检查
        print("开始格式检查...")
        rule_engine = RuleEngine(rules)
        issues = rule_engine.check_document(elements)
        
        # 按严重程度过滤
        if args.severity:
            issues = rule_engine.filter_issues_by_severity(issues, args.severity)
        
        print(f"检查完成，发现 {len(issues)} 个问题")
        
        # 格式化结果
        formatter = ResultFormatter()
        
        # 生成报告内容
        if args.format == "json":
            report_content = formatter.format_to_json(
                issues, args.document, config_path, statistics
            )
        elif args.format == "html":
            report_content = formatter.generate_html_report(
                issues, args.document, config_path, statistics
            )
        else:  # console
            report_content = formatter.format_to_console(issues, args.document)
        
        # 输出结果
        if args.output:
            formatter.save_to_file(report_content, args.output)
            if not args.quiet:
                print(f"报告已保存到: {args.output}")
        
        if not args.quiet:
            if args.format == "console" or not args.output:
                print(report_content)
            elif args.format in ["json", "html"] and args.output:
                # 如果是JSON或HTML格式且指定了输出文件，在控制台显示摘要
                summary_report = formatter.format_to_console(issues, args.document)
                print(summary_report)
        
        # 返回退出码（有严重问题时返回1）
        critical_issues = [i for i in issues if i.severity == "critical"]
        return 1 if critical_issues else 0
        
    except FileNotFoundError as e:
        print(f"错误: {e}", file=sys.stderr)
        return 1
    except Exception as e:
        print(f"程序执行错误: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())