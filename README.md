# Word文档格式检查工具 MVP 版本

## 项目简介

这是一个基于Python的Word文档格式检查工具，可以自动检查Word文档是否符合指定的格式规范。工具支持检查字体、字号、行间距、段间距、对齐方式等多种格式要求。

## 功能特性

- 🔍 **格式检查**：检查正文、各级标题的字体、字号、样式
- 🈶 **中英文支持**：区分中英文字符，应用不同字体规则
- 📏 **间距检查**：检查行间距、段前段后间距、首行缩进
- ⚙️ **配置灵活**：使用YAML配置文件，支持自定义规则
- 📊 **多种输出**：支持控制台、JSON、HTML格式报告
- 🎯 **精确定位**：提供段落索引定位和上下文展示

## 项目结构

```
doc_check/
├── main.py                 # 主程序入口
├── config_manager.py       # 配置管理
├── doc_parser.py          # Word文档解析
├── rule_engine.py         # 规则引擎
├── result_formatter.py    # 结果格式化
├── checkers/              # 检查器模块
│   ├── __init__.py
│   ├── base_checker.py
│   ├── font_checker.py
│   ├── spacing_checker.py
│   └── style_checker.py
├── config/
│   └── default_rules.yaml # 默认规则配置
├── test/                   # 测试文件
├── logs/                  # 日志目录
└── requirements.txt       # 依赖包
```

## 安装和运行

### 1. 依赖安装

```bash
# 使用项目指定的Python解释器
/opt/miniconda3/envs/wordscopy/bin/pip install -r requirements.txt
```

### 2. 基本使用

```bash
# 检查Word文档并输出到控制台
/opt/miniconda3/envs/wordscopy/bin/python main.py document.docx

# 生成JSON格式报告
/opt/miniconda3/envs/wordscopy/bin/python main.py document.docx --format json --output report.json

# 生成HTML格式报告
/opt/miniconda3/envs/wordscopy/bin/python main.py document.docx --format html --output report.html

# 使用自定义配置文件
/opt/miniconda3/envs/wordscopy/bin/python main.py document.docx -c custom_rules.yaml

# 只显示严重问题
/opt/miniconda3/envs/wordscopy/bin/python main.py document.docx --severity critical
```

### 3. 参数说明

- `document`: 要检查的Word文档路径（必需）
- `-c, --config`: 自定义配置文件路径
- `-o, --output`: 输出报告文件路径
- `--format`: 输出格式（console/json/html）
- `--severity`: 过滤问题严重级别（critical/warning/info）
- `--quiet`: 静默模式，只输出到文件

## 配置文件说明

默认配置文件位于 `config/default_rules.yaml`，包含以下主要配置项：

### 正文格式规则
```yaml
body:
  font:
    chinese: "宋体"           # 中文字体
    english: "Times New Roman" # 英文字体
  size: 12                    # 字号（磅）
  line_spacing: 1.5           # 行间距（倍数）
  alignment: "justify"        # 对齐方式
  first_line_indent: 2        # 首行缩进（字符）
```

### 标题格式规则
```yaml
headings:
  level_1:
    font:
      chinese: "黑体"
      english: "Arial"
    size: 16
    bold: true
    line_spacing: 1.5
    spacing_before: 12        # 段前间距（磅）
    spacing_after: 6          # 段后间距（磅）
    alignment: "center"
```

## 问题严重级别

- **Critical（严重）**：字体、字号等核心格式问题
- **Warning（警告）**：对齐方式、行间距等重要格式问题  
- **Info（信息）**：段间距、缩进等次要格式问题

## 测试

运行测试脚本：

```bash
/opt/miniconda3/envs/wordscopy/bin/python test/test_checker.py
```

创建测试文档：

```bash
/opt/miniconda3/envs/wordscopy/bin/python test/create_test_doc.py
```

## MVP版本限制

当前版本为MVP实现，包含以下限制：

1. 不支持精确页码定位（使用段落索引代替）
2. 不支持复杂图表格式检查
3. 不提供自动修复功能
4. 不包含GUI界面

## 技术实现

- **文档解析**：使用python-docx库解析Word文档
- **中英文识别**：基于Unicode字符范围判断
- **标题识别**：基于样式名称和大纲级别
- **配置验证**：使用jsonschema验证配置文件
- **设计模式**：使用策略模式实现不同检查器

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

本项目采用MIT许可证。