#!/opt/miniconda3/envs/wordscopy/bin/python
"""配置管理器模块"""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path
import jsonschema


class ConfigManager:
    """配置文件管理器"""
    
    def __init__(self):
        self.config: Optional[Dict[str, Any]] = None
        self.config_schema = self._get_config_schema()
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict[str, Any]: 配置数据
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
            jsonschema.ValidationError: 配置格式验证失败
        """
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 验证配置格式
            self._validate_config(config_data)
            
            self.config = config_data
            return config_data
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"YAML文件格式错误: {e}")
        except jsonschema.ValidationError as e:
            raise jsonschema.ValidationError(f"配置文件格式验证失败: {e.message}")
    
    def get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_dir = Path(__file__).parent
        return str(current_dir / "config" / "default_rules.yaml")
    
    def get_body_rules(self) -> Dict[str, Any]:
        """获取正文规则"""
        if not self.config:
            raise ValueError("配置文件未加载")
        return self.config.get("body", {})
    
    def get_heading_rules(self, level: int) -> Dict[str, Any]:
        """
        获取标题规则
        
        Args:
            level: 标题级别 (1-6)
            
        Returns:
            Dict[str, Any]: 标题规则
        """
        if not self.config:
            raise ValueError("配置文件未加载")
        
        headings = self.config.get("headings", {})
        return headings.get(f"level_{level}", {})
    
    def get_caption_rules(self, caption_type: str) -> Dict[str, Any]:
        """
        获取图表标题规则
        
        Args:
            caption_type: 标题类型 (figure, table)
            
        Returns:
            Dict[str, Any]: 图表标题规则
        """
        if not self.config:
            raise ValueError("配置文件未加载")
        
        captions = self.config.get("captions", {})
        return captions.get(caption_type, {})
    
    def get_options(self) -> Dict[str, Any]:
        """获取检查选项"""
        if not self.config:
            raise ValueError("配置文件未加载")
        return self.config.get("options", {})
    
    def _validate_config(self, config_data: Dict[str, Any]) -> None:
        """验证配置文件格式"""
        try:
            jsonschema.validate(config_data, self.config_schema)
        except jsonschema.ValidationError as e:
            raise jsonschema.ValidationError(f"配置验证失败: {e.message}")
    
    def _get_config_schema(self) -> Dict[str, Any]:
        """获取配置文件JSON Schema"""
        return {
            "type": "object",
            "required": ["version", "name"],
            "properties": {
                "version": {"type": "string"},
                "name": {"type": "string"},
                "body": {
                    "type": "object",
                    "properties": {
                        "font": {
                            "type": "object",
                            "properties": {
                                "chinese": {"type": "string"},
                                "english": {"type": "string"}
                            }
                        },
                        "size": {"type": "number"},
                        "line_spacing": {"type": "number"},
                        "alignment": {"type": "string"},
                        "first_line_indent": {"type": "number"}
                    }
                },
                "headings": {
                    "type": "object",
                    "patternProperties": {
                        "^level_[1-6]$": {
                            "type": "object",
                            "properties": {
                                "font": {
                                    "type": "object",
                                    "properties": {
                                        "chinese": {"type": "string"},
                                        "english": {"type": "string"}
                                    }
                                },
                                "size": {"type": "number"},
                                "bold": {"type": "boolean"},
                                "line_spacing": {"type": "number"},
                                "spacing_before": {"type": "number"},
                                "spacing_after": {"type": "number"},
                                "alignment": {"type": "string"}
                            }
                        }
                    }
                },
                "captions": {
                    "type": "object",
                    "properties": {
                        "figure": {
                            "type": "object",
                            "properties": {
                                "font": {
                                    "type": "object",
                                    "properties": {
                                        "chinese": {"type": "string"},
                                        "english": {"type": "string"}
                                    }
                                },
                                "size": {"type": "number"},
                                "bold": {"type": "boolean"},
                                "alignment": {"type": "string"},
                                "position": {"type": "string"}
                            }
                        },
                        "table": {
                            "type": "object",
                            "properties": {
                                "font": {
                                    "type": "object",
                                    "properties": {
                                        "chinese": {"type": "string"},
                                        "english": {"type": "string"}
                                    }
                                },
                                "size": {"type": "number"},
                                "bold": {"type": "boolean"},
                                "alignment": {"type": "string"},
                                "position": {"type": "string"}
                            }
                        }
                    }
                },
                "options": {
                    "type": "object",
                    "properties": {
                        "check_empty_paragraphs": {"type": "boolean"},
                        "check_spacing_consistency": {"type": "boolean"},
                        "strict_mode": {"type": "boolean"}
                    }
                }
            }
        }