#!/opt/miniconda3/envs/wordscopy/bin/python
"""规则检查引擎模块"""

from typing import List, Dict, Any
from checkers.base_checker import Issue
from checkers.font_checker import <PERSON>ont<PERSON>hecker
from checkers.spacing_checker import Spacing<PERSON>hecker
from checkers.style_checker import <PERSON><PERSON><PERSON><PERSON>
from doc_parser import DocumentElement


class RuleEngine:
    """规则检查引擎"""
    
    def __init__(self, rules: Dict[str, Any]):
        """
        初始化规则引擎
        
        Args:
            rules: 检查规则配置
        """
        self.rules = rules
        self.checkers = self._initialize_checkers()
    
    def _initialize_checkers(self) -> List:
        """初始化所有检查器"""
        return [
            <PERSON><PERSON><PERSON><PERSON><PERSON>(self.rules),
            Spa<PERSON><PERSON><PERSON><PERSON>(self.rules),
            <PERSON><PERSON><PERSON><PERSON>(self.rules)
        ]
    
    def check_document(self, elements: List[DocumentElement]) -> List[Issue]:
        """
        检查整个文档
        
        Args:
            elements: 文档元素列表
            
        Returns:
            List[Issue]: 所有发现的问题
        """
        all_issues = []
        
        for element in elements:
            element_issues = self.check_element(element)
            all_issues.extend(element_issues)
        
        return all_issues
    
    def check_element(self, element: DocumentElement) -> List[Issue]:
        """
        检查单个文档元素
        
        Args:
            element: 文档元素
            
        Returns:
            List[Issue]: 该元素的所有问题
        """
        issues = []
        
        for checker in self.checkers:
            try:
                checker_issues = checker.check(
                    element.paragraph,
                    element.element_type,
                    element.paragraph_index
                )
                issues.extend(checker_issues)
            except Exception as e:
                # 记录检查器错误，但不中断整体检查
                print(f"检查器错误 ({type(checker).__name__}): {e}")
        
        return issues
    
    def filter_issues_by_severity(self, issues: List[Issue], 
                                 severity_levels: List[str]) -> List[Issue]:
        """
        按严重级别过滤问题
        
        Args:
            issues: 问题列表
            severity_levels: 要保留的严重级别列表
            
        Returns:
            List[Issue]: 过滤后的问题列表
        """
        return [issue for issue in issues 
                if issue.severity in severity_levels]
    
    def group_issues_by_type(self, issues: List[Issue]) -> Dict[str, List[Issue]]:
        """
        按问题类型分组
        
        Args:
            issues: 问题列表
            
        Returns:
            Dict[str, List[Issue]]: 按类型分组的问题
        """
        grouped = {}
        for issue in issues:
            if issue.type not in grouped:
                grouped[issue.type] = []
            grouped[issue.type].append(issue)
        
        return grouped
    
    def get_issues_summary(self, issues: List[Issue]) -> Dict[str, int]:
        """
        获取问题统计摘要
        
        Args:
            issues: 问题列表
            
        Returns:
            Dict[str, int]: 问题统计信息
        """
        summary = {
            "total_issues": len(issues),
            "critical": 0,
            "warning": 0,
            "info": 0
        }
        
        for issue in issues:
            if issue.severity in summary:
                summary[issue.severity] += 1
        
        return summary
    
    def should_check_element_type(self, element_type: str) -> bool:
        """
        检查是否应该检查该元素类型
        
        Args:
            element_type: 元素类型
            
        Returns:
            bool: 是否应该检查
        """
        options = self.rules.get("options", {})
        
        # 检查空段落选项
        if element_type == "empty_paragraph":
            return options.get("check_empty_paragraphs", True)
        
        # 其他元素类型默认检查
        return True
    
    def is_strict_mode(self) -> bool:
        """检查是否启用严格模式"""
        options = self.rules.get("options", {})
        return options.get("strict_mode", False)