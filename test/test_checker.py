#!/opt/miniconda3/envs/wordscopy/bin/python
"""格式检查工具测试脚本"""

import sys
import os
import subprocess

# 添加项目根目录到Python路径
sys.path.insert(0, '/root/workspace/doc_check')

from config_manager import ConfigManager
from doc_parser import DocParser
from rule_engine import RuleEngine
from result_formatter import ResultFormatter


def test_config_manager():
    """测试配置管理器"""
    print("测试配置管理器...")
    
    try:
        config_manager = ConfigManager()
        config_path = config_manager.get_default_config_path()
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        rules = config_manager.load_config(config_path)
        
        # 检查必要的配置项
        required_keys = ['version', 'name', 'body', 'headings']
        for key in required_keys:
            if key not in rules:
                print(f"❌ 缺少配置项: {key}")
                return False
        
        print("✅ 配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False


def test_doc_parser():
    """测试文档解析器"""
    print("测试文档解析器...")
    
    try:
        # 首先创建测试文档
        create_test_script = '/root/workspace/doc_check/test/create_test_doc.py'
        subprocess.run(['/opt/miniconda3/envs/wordscopy/bin/python', create_test_script], 
                      check=True, capture_output=True)
        
        test_doc_path = '/root/workspace/doc_check/test/test_document.docx'
        
        if not os.path.exists(test_doc_path):
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return False
        
        doc_parser = DocParser()
        document = doc_parser.load_document(test_doc_path)
        elements = doc_parser.parse_elements()
        statistics = doc_parser.get_document_statistics()
        
        print(f"   解析到 {len(elements)} 个元素")
        print(f"   统计信息: {statistics}")
        
        if len(elements) == 0:
            print("❌ 没有解析到任何元素")
            return False
        
        print("✅ 文档解析器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文档解析器测试失败: {e}")
        return False


def test_rule_engine():
    """测试规则检查引擎"""
    print("测试规则检查引擎...")
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config_path = config_manager.get_default_config_path()
        rules = config_manager.load_config(config_path)
        
        # 解析文档
        test_doc_path = '/root/workspace/doc_check/test/test_document.docx'
        doc_parser = DocParser()
        document = doc_parser.load_document(test_doc_path)
        elements = doc_parser.parse_elements()
        
        # 执行检查
        rule_engine = RuleEngine(rules)
        issues = rule_engine.check_document(elements)
        
        print(f"   发现 {len(issues)} 个问题")
        
        # 按严重程度统计
        summary = rule_engine.get_issues_summary(issues)
        print(f"   问题统计: {summary}")
        
        # 我们期望发现一些问题（测试文档故意包含错误）
        if len(issues) == 0:
            print("⚠️  警告：没有发现任何问题，可能检查器有问题")
        
        print("✅ 规则检查引擎测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 规则检查引擎测试失败: {e}")
        return False


def test_result_formatter():
    """测试结果格式化器"""
    print("测试结果格式化器...")
    
    try:
        # 运行完整检查流程
        config_manager = ConfigManager()
        config_path = config_manager.get_default_config_path()
        rules = config_manager.load_config(config_path)
        
        test_doc_path = '/root/workspace/doc_check/test/test_document.docx'
        doc_parser = DocParser()
        document = doc_parser.load_document(test_doc_path)
        elements = doc_parser.parse_elements()
        statistics = doc_parser.get_document_statistics()
        
        rule_engine = RuleEngine(rules)
        issues = rule_engine.check_document(elements)
        
        # 测试格式化器
        formatter = ResultFormatter()
        
        # 测试控制台输出
        console_report = formatter.format_to_console(issues, test_doc_path)
        if not console_report:
            print("❌ 控制台格式输出为空")
            return False
        
        # 测试JSON输出
        json_report = formatter.format_to_json(issues, test_doc_path, config_path, statistics)
        if not json_report:
            print("❌ JSON格式输出为空")
            return False
        
        # 测试HTML输出
        html_report = formatter.generate_html_report(issues, test_doc_path, config_path, statistics)
        if not html_report:
            print("❌ HTML格式输出为空")
            return False
        
        print("✅ 结果格式化器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 结果格式化器测试失败: {e}")
        return False


def test_main_program():
    """测试主程序"""
    print("测试主程序...")
    
    try:
        test_doc_path = '/root/workspace/doc_check/test/test_document.docx'
        main_script = '/root/workspace/doc_check/main.py'
        
        # 测试控制台输出
        result = subprocess.run([
            '/opt/miniconda3/envs/wordscopy/bin/python', 
            main_script, 
            test_doc_path
        ], capture_output=True, text=True, cwd='/root/workspace/doc_check')
        
        if result.returncode not in [0, 1]:  # 0表示无问题，1表示有问题
            print(f"❌ 主程序执行失败: {result.stderr}")
            return False
        
        if not result.stdout:
            print("❌ 主程序没有输出")
            return False
        
        print("✅ 主程序测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始运行格式检查工具测试...")
    print("=" * 50)
    
    tests = [
        test_config_manager,
        test_doc_parser,
        test_rule_engine,
        test_result_formatter,
        test_main_program
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)