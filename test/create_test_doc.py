#!/opt/miniconda3/envs/wordscopy/bin/python
"""创建测试Word文档"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH


def create_test_document():
    """创建一个测试文档，包含各种格式问题"""
    
    doc = Document()
    
    # 添加标题1 - 格式错误（应该居中，应该是黑体16磅）
    h1 = doc.add_heading('第一章 引言', level=1)
    h1.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 错误：应该居中
    for run in h1.runs:
        run.font.name = '宋体'  # 错误：应该是黑体
        run.font.size = Pt(14)  # 错误：应该是16磅
    
    # 添加正文段落 - 字体错误
    p1 = doc.add_paragraph()
    run1 = p1.add_run('这是一段中文正文，使用了错误的字体格式。')
    run1.font.name = '微软雅黑'  # 错误：应该是宋体
    run1.font.size = Pt(12)
    
    run2 = p1.add_run(' This is English text with wrong font.')
    run2.font.name = '宋体'  # 错误：英文应该是Times New Roman
    run2.font.size = Pt(12)
    
    # 添加标题2 - 行间距错误
    h2 = doc.add_heading('1.1 研究背景', level=2)
    h2.alignment = WD_ALIGN_PARAGRAPH.LEFT
    h2_format = h2.paragraph_format
    h2_format.line_spacing = 1.0  # 错误：应该是1.5
    for run in h2.runs:
        run.font.name = '黑体'
        run.font.size = Pt(14)
        run.bold = True
    
    # 添加正文段落 - 首行缩进错误
    p2 = doc.add_paragraph()
    p2_format = p2.paragraph_format
    p2_format.first_line_indent = Pt(24)  # 错误：应该是24磅（2字符）
    p2_format.line_spacing = 1.5
    
    run3 = p2.add_run('研究表明，文档格式规范对于提高阅读体验具有重要意义。')
    run3.font.name = '宋体'
    run3.font.size = Pt(12)
    
    run4 = p2.add_run(' Research shows that document formatting standards are crucial.')
    run4.font.name = 'Times New Roman'
    run4.font.size = Pt(12)
    
    # 添加标题3 - 段前段后间距错误
    h3 = doc.add_heading('1.1.1 问题描述', level=3)
    h3_format = h3.paragraph_format
    h3_format.space_before = Pt(3)  # 错误：应该是6磅
    h3_format.space_after = Pt(0)   # 错误：应该是3磅
    for run in h3.runs:
        run.font.name = '黑体'
        run.font.size = Pt(13)
        run.bold = True
    
    # 添加正文 - 字号错误
    p3 = doc.add_paragraph()
    run5 = p3.add_run('当前很多文档缺乏统一的格式规范，影响了文档的专业性和可读性。')
    run5.font.name = '宋体'
    run5.font.size = Pt(11)  # 错误：应该是12磅
    
    # 保存文档
    doc.save('/root/workspace/doc_check/test/test_document.docx')
    print("测试文档已创建: /root/workspace/doc_check/test/test_document.docx")


if __name__ == "__main__":
    create_test_document()